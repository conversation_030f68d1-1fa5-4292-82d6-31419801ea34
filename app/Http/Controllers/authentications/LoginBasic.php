<?php

namespace App\Http\Controllers\authentications;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Auth;

class LoginBasic extends Controller
{
  public function index()
  {
    return view('auth.login');
  }

  public function isolir()
  {
    return view('/isolir/isolir-page', [
      'users' => auth()->user(),
      'roles' => auth()->user()->roles,
    ]);
  }

  public function login(Request $request)
  {
    $credentials = $request->only('email', 'password');

    if (auth()->attempt($credentials)) {
      if (auth()->user()->roles_id == 8) {
        return redirect()->intended('/customer')->with('toast_success', 'Selamat Datang di E-Nagih ' . auth()->user()->name);
      }
      return redirect()->intended('dashboard')->with('toast_success', 'Login successful!' . auth()->user()->name);
    }

    return redirect()->back()->with('toast_error', 'Username atau Password salah!');
  }

  public function logout()
  {
    auth()->logout();
    return redirect('/')->with('toast_success', 'Logout successful!');
  }

}
