@extends('layouts.contentNavbarLayout')

@section('title', '<PERSON> Pelanggan')

@section('page-style')
    <style>
        /* Header Styles */
        .modern-card-header {
            padding: 1.25rem 1.25rem 0.75rem;
            background-color: #ffffff;
            border-bottom: none;
            position: relative;
        }

        .card {
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .card-header {
            background-color: #ffffff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem;
        }

        .header-title {
            font-size: 1.2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            position: relative;
            padding-bottom: 0.5rem;
            font-weight: 600;
        }

        .header-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            height: 2px;
            width: 40px;
            background-color: #5e72e4;
            border-radius: 2px;
        }

        /* Data Card Styles */
        .data-card {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            padding: 0.85rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            border-left: 3px solid transparent;
        }

        .data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .data-card.primary-card {
            border-left-color: #5e72e4;
        }

        .data-card.success-card {
            border-left-color: #2dce89;
        }

        .data-card.danger-card {
            border-left-color: #f5365c;
        }

        .data-card.secondary-card {
            border-left-color: #8898aa;
        }

        .data-card-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            position: relative;
            transition: all 0.2s ease;
        }

        .data-card:hover .data-card-icon {
            transform: scale(1.05);
        }

        .data-card-icon.primary {
            background-color: rgba(94, 114, 228, 0.1);
            color: #5e72e4;
        }

        .data-card-icon.success {
            background-color: rgba(45, 206, 137, 0.1);
            color: #2dce89;
        }

        .data-card-icon.danger {
            background-color: rgba(245, 54, 92, 0.1);
            color: #f5365c;
        }

        .data-card-icon.secondary {
            background-color: rgba(136, 152, 170, 0.1);
            color: #8898aa;
        }

        .data-card-content {
            flex: 1;
        }

        .data-label {
            font-size: 0.7rem;
            color: #8898aa;
            margin-bottom: 0.2rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .data-value {
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.2;
        }

        .data-value .badge {
            font-size: 0.75rem;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
        }

        /* Search Styles */
        .search-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            padding: 0.75rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }

        .search-container:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .search-input {
            border-radius: 4px 0 0 4px;
            border: 1px solid #e9ecef;
            padding: 0.5rem 0.75rem;
            font-size: 0.85rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            box-shadow: 0 0 0 0.15rem rgba(94, 114, 228, 0.1);
            border-color: #5e72e4;
        }

        .search-button {
            border-radius: 0 4px 4px 0;
            background-color: #5e72e4;
            border: none;
            color: white;
            padding: 0.5rem 0.75rem;
            transition: all 0.2s ease;
        }

        .search-button:hover {
            background-color: #4a5cd0;
        }

        .divider {
            height: 1px;
            background-color: rgba(0, 0, 0, 0.05);
            margin: 1rem 0;
            border: none;
        }

        /* Modern Table Styles */
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .modern-table thead {
            background-color: #f8f9fa;
        }

        .modern-table th {
            padding: 1rem;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .modern-table td {
            padding: 0.85rem 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #f1f1f1;
            color: #495057;
            font-size: 0.9rem;
        }

        .modern-table tbody tr {
            transition: background-color 0.2s;
        }

        .modern-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Action Buttons */
        .action-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            margin: 0 3px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn i {
            font-size: 1rem;
        }

        /* Status Badges */
        .status-badge {
            padding: 0.4rem 0.8rem;
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        /* No Results Message */
        .no-results {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-style: italic;
            display: none;
        }

        /* Payment Confirmation Modal Styles - Minimalist Version */
        .payment-modal .modal-dialog {
            max-width: 480px;
            margin: 1.75rem auto;
        }

        @media (max-width: 576px) {
            .payment-modal .modal-dialog {
                max-width: 95%;
                margin: 0.5rem auto;
            }
        }

        .payment-modal .modal-content {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            background-color: #ffffff;
        }

        .payment-modal .modal-header {
            background-color: #5e72e4;
            border-bottom: none;
            padding: 1rem 1.25rem;
            position: relative;
        }

        .payment-modal .modal-title {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
        }

        .payment-modal .modal-title i {
            margin-right: 0.5rem;
            font-size: 1.1rem;
        }

        .payment-modal .modal-body {
            padding: 1.25rem;
        }

        .payment-modal .modal-footer {
            border-top: 1px solid #f1f1f1;
            padding: 1rem 1.25rem;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        @media (max-width: 576px) {
            .payment-modal .modal-footer {
                justify-content: space-between;
            }

            .payment-modal .btn-cancel,
            .payment-modal .btn-confirm {
                flex: 1;
            }
        }

        .payment-modal .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.1rem;
            line-height: 1;
            padding: 4px;
            margin: 0;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .payment-modal .close-btn:hover {
            opacity: 1;
        }

        .payment-modal .customer-info {
            border-left: 2px solid #5e72e4;
            padding: 0.5rem 0 0.5rem 0.75rem;
            margin-bottom: 1.25rem;
        }

        .payment-modal .info-item {
            margin-bottom: 0.5rem;
            display: flex;
            flex-wrap: wrap;
        }

        .payment-modal .info-item:last-child {
            margin-bottom: 0;
        }

        .payment-modal .info-label {
            font-size: 0.75rem;
            color: #8898aa;
            font-weight: 500;
            width: 120px;
            flex-shrink: 0;
        }

        @media (max-width: 576px) {
            .payment-modal .info-label {
                width: 100%;
                margin-bottom: 0.125rem;
            }

            .payment-modal .info-value {
                width: 100%;
                padding-left: 0.25rem;
            }
        }

        .payment-modal .info-value {
            font-size: 0.875rem;
            color: #2c3e50;
            font-weight: 600;
            flex-grow: 1;
        }

        .payment-modal .form-label {
            font-size: 0.8rem;
            color: #495057;
            font-weight: 500;
            margin-bottom: 0.375rem;
        }

        .payment-modal .form-control {
            border-radius: 4px;
            border: 1px solid #e9ecef;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .payment-modal .form-control:focus {
            border-color: #5e72e4;
            box-shadow: none;
        }

        .payment-modal .form-select {
            border-radius: 4px;
            border: 1px solid #e9ecef;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .payment-modal .form-select:focus {
            border-color: #5e72e4;
            box-shadow: none;
        }

        .payment-modal .btn-confirm {
            background-color: #5e72e4;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: background-color 0.2s;
            color: white;
        }

        .payment-modal .btn-confirm:hover {
            background-color: #4a5cd0;
        }

        .payment-modal .btn-cancel {
            background-color: transparent;
            border: 1px solid #e9ecef;
            color: #495057;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }

        .payment-modal .btn-cancel:hover {
            background-color: #f8f9fa;
        }

        .payment-modal .payment-amount {
            font-size: 1.5rem;
            font-weight: 600;
            color: #5e72e4;
            text-align: center;
            margin: 1rem 0;
        }

        .payment-modal .payment-amount .currency {
            font-size: 1rem;
            margin-right: 0.25rem;
            font-weight: 500;
        }

        .payment-modal .payment-details {
            margin-top: 1.25rem;
        }

        .payment-modal .payment-method-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 0.375rem;
        }

        .payment-modal .payment-method-option {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            cursor: pointer;
            transition: border-color 0.2s;
            flex: 1;
            min-width: 120px;
        }

        .payment-modal .payment-method-option:hover {
            border-color: #5e72e4;
        }

        .payment-modal .payment-method-option.selected {
            border-color: #5e72e4;
            background-color: rgba(94, 114, 228, 0.05);
        }

        .payment-modal .payment-method-icon {
            color: #5e72e4;
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .payment-modal .payment-method-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #495057;
        }
    </style>
@endsection

@section('content')
    <div class="row">
        <!-- Card with data summary -->
        <div class="col-sm-12 mb-4">
            <div class="card">
                <div class="card-header modern-card-header">
                    <h4 class="header-title">Data Global</h4>
                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="data-card bg-white primary-card">
                                <div class="data-card-icon primary">
                                    <i class="bx bx-user"></i>
                                </div>
                                <div class="data-card-content">
                                    <div class="data-label">Jumlah Pelanggan</div>
                                    <div class="data-value">
                                        <span class="badge bg-primary rounded-pill">
                                            {{ count($data) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="data-card bg-white success-card">
                                <div class="data-card-icon success">
                                    <i class="bx bx-check-circle"></i>
                                </div>
                                <div class="data-card-content">
                                    <div class="data-label">Pelanggan Aktif</div>
                                    <div class="data-value">
                                        <span class="badge bg-success rounded-pill">
                                            @php
                                                $aktif = 0;
                                                foreach ($data as $item) {
                                                    if ($item->status_id == 3) {
                                                        $aktif++;
                                                    }
                                                }
                                                echo $aktif;
                                            @endphp
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <div class="data-card bg-white danger-card">
                                <div class="data-card-icon danger">
                                    <i class="bx bx-x-circle"></i>
                                </div>
                                <div class="data-card-content">
                                    <div class="data-label">Pelanggan Non-Aktif</div>
                                    <div class="data-value">
                                        <span class="badge bg-danger rounded-pill">
                                            @php
                                                $nonaktif = 0;
                                                foreach ($data as $item) {
                                                    if ($item->status_id != 3) {
                                                        $nonaktif++;
                                                    }
                                                }
                                                echo $nonaktif;
                                            @endphp
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if (auth()->user()->roles_id == 1)
                            <div class="col-md-6 col-lg-3 mb-3">
                                <div class="data-card bg-white primary-card">
                                    <div class="data-card-icon primary">
                                        <i class="bx bx-money"></i>
                                    </div>
                                    <a href="/pembayaran">
                                        <div class="data-card-content">
                                            <div class="data-label">Konfirmasi Pembayaran</div>
                                            <div class="data-value">
                                                <span class="badge bg-primary rounded-pill">
                                                    0
                                                </span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Card with search and table -->
        <div class="col-sm-12">
            <div class="card">
                <div class="card-header modern-card-header mb-5">
                    <h4 class="header-title">Daftar Pelanggan</h4>
                    <div class="row">
                        <div class="col-sm-12 col-md-4">
                            <div class="search-container">
                                <div class="input-group">
                                    <input type="text" class="form-control search-input" placeholder="Cari pelanggan..."
                                        id="searchCustomer">
                                    <button class="btn search-button" type="button" id="searchButton">
                                        <i class="bx bx-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- WebSocket Connection Status -->
                    <div class="row mb-3" style="display: none">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title">Status Koneksi WebSocket</h5>
                                    <div id="status" class="alert alert-info">Menghubungkan ke server WebSocket...</div>
                                    <div id="messages" class="small text-muted"
                                        style="max-height: 150px; overflow-y: auto;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive mb-2">
                        <table class="table modern-table" id="customerTable">
                            <thead>
                                <tr class="text-center">
                                    <th>No</th>
                                    <th>Nama</th>
                                    <th>Alamat</th>
                                    <th>Telp.</th>
                                    <th>BTS Server</th>
                                    <th>Paket</th>
                                    <th>Status</th>
                                    <th>Tagihan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($data as $item)
                                    <tr class="customer-row" data-id="{{ $item->id }}"
                                        data-tagihan="{{ $item->invoice->isNotEmpty() && $item->invoice->first()->status ? ($item->invoice->first()->status->nama_status == 'Sudah Bayar' ? '0' : $item->tagihan ?? '0') : '0' }}">
                                        <td class="text-center">{{ $loop->iteration }}</td>
                                        <td class="customer-name">{{ $item->nama_customer }}</td>
                                        <td class="customer-address">{{ $item->alamat }}</td>
                                        <td>{{ $item->no_hp }}</td>
                                        <td>{{ $item->getServer->lokasi_server }}</td>
                                        <td>
                                            <span class="badge bg-warning bg-opacity-10 status-badge text-warning">
                                                {{ $item->paket->nama_paket }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            @if ($item->status_id == 3)
                                                <span class="badge bg-success bg-opacity-10 text-success status-badge">
                                                    Aktif
                                                </span>
                                            @elseif($item->status_id == 9)
                                                <span class="badge bg-danger status-badge">
                                                    Blokir
                                                </span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if ($item->invoice->isNotEmpty() && $item->invoice->first()->status)
                                                <span
                                                    class="badge bg-{{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'success' : 'danger' }} status-badge bg-opacity-10 text-{{ $item->invoice->first()->status->nama_status == 'Sudah Bayar' ? 'success' : 'danger' }}">
                                                    {{ $item->invoice->first()->status->nama_status }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary status-badge">
                                                    Belum Ada Invoice
                                                </span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if (auth()->user()->roles_id == 1 || auth()->user()->roles_id == 2)
                                                <a href="#" class="btn btn-success action-btn add-customer-btn mb-2"
                                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                                    title="Konfirmasi Pembayaran">
                                                    <i class="bx bx-money"></i>
                                                </a>
                                                <a href="/blokir/{{ $item->id }}"
                                                    class="btn btn-danger action-btn blokir-customer-btn mb-2"
                                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                                    title="Blokir Pelanggan">
                                                    <i class="bx bx-block"></i>
                                                </a>
                                                <a href="/unblokir/{{ $item->id }}"
                                                    class="btn btn-warning action-btn unblokir-customer-btn mb-2"
                                                    data-bs-toggle="tooltip" data-bs-placement="top"
                                                    title="Aktifkan Pelanggan">
                                                    <i class="bx bx-check-circle"></i>
                                                </a>
                                            @endif
                                            <a href="/detail-pelanggan/{{ $item->id }}"
                                                class="btn btn-info action-btn detail-customer-btn mb-2"
                                                data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Detail Pelanggan">
                                                <i class="bx bx-show"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="no-results" id="noResults">
                            <p>Tidak ada data pelanggan yang sesuai dengan pencarian.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Payment Confirmation Modal - Minimalist Version -->
    <div class="modal fade payment-modal" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">
                        <i class="bx bx-money"></i>Pembayaran
                    </h5>
                    <button type="button" class="close-btn" data-bs-dismiss="modal" aria-label="Close"
                        style="margin-left: auto;">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm" enctype="multipart/form-data" method="post"
                        action="/konfirmasi-pembayaran/{{ $item->id }}">
                        @csrf
                        <!-- Customer Information -->
                        <div class="customer-info">
                            <div class="info-item">
                                <div class="info-label">Nama</div>
                                <div class="info-value" id="customerName">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Paket</div>
                                <div class="info-value" id="customerPackage">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Status</div>
                                <div class="info-value" id="billStatus">-</div>
                            </div>
                        </div>
                        <hr>
                        <!-- Payment Amount -->
                        <div class="payment-amount">
                            <span class="currency">Rp</span><span id="paymentAmount">0</span>
                        </div>
                        <hr>
                        <!-- Payment Details -->
                        <div class="payment-details">
                            <div class="mb-3">
                                <label for="paymentDate" class="form-label">Tanggal Pembayaran</label>
                                <input type="date" class="form-control" id="paymentDate" name="paymentDate" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Metode Pembayaran</label>
                                <select class="form-select" id="paymentMethodSelect" name="paymentMethodSelect">
                                    @foreach ($metode as $item)
                                        <option value="{{ $item->id }}">{{ $item->nama_metode }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-3" id="transferDetails" style="display: none;">
                                <label for="transferProof" class="form-label">Bukti Pembayaran</label>
                                <input type="file" class="form-control" id="transferProof" name="transferProof"
                                    accept="image/*,.pdf">
                                <small class="form-text text-muted">Format: JPG, PNG, PDF (Maks. 5MB)</small>
                            </div>

                            <div class="mb-3">
                                <label for="paymentNotes" class="form-label">Catatan</label>
                                <textarea class="form-control" id="paymentNotes" name="paymentNotes" rows="2"
                                    placeholder="Tambahkan catatan jika diperlukan"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer mt-5 gap-3">
                            <button type="button" class="btn btn-danger btn-sm" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-warning btn-sm">Konfirmasi</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection


@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchCustomer');
            const searchButton = document.getElementById('searchButton');
            const customerRows = document.querySelectorAll('.customer-row');
            const noResultsMessage = document.getElementById('noResults');
            const customerTable = document.getElementById('customerTable');

            // Payment Modal Elements
            const paymentButtons = document.querySelectorAll('.add-customer-btn');
            const paymentModalEl = document.getElementById('paymentModal');
            const paymentModal = new bootstrap.Modal(paymentModalEl);
            const paymentMethodSelect = document.getElementById('paymentMethodSelect');
            const transferDetails = document.getElementById('transferDetails');
            const paymentDateInput = document.getElementById('paymentDate');

            // Set default payment date to today
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10);
            paymentDateInput.value = formattedDate;

            // Function to create and show notification
            function showNotification(customer, dueDate) {
                // Format due date if available
                let dueDateText = '';
                if (dueDate) {
                    const formattedDueDate = new Date(dueDate).toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    });
                    dueDateText = `<br><small>Jatuh tempo: ${formattedDueDate}</small>`;
                }
            }

            // Function to perform search
            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase().trim();
                let resultsFound = false;

                // If search is empty, show all rows
                if (searchTerm === '') {
                    customerRows.forEach(row => {
                        row.style.display = '';
                    });
                    noResultsMessage.style.display = 'none';
                    customerTable.style.display = '';
                    return;
                }

                // Filter rows based on search term
                customerRows.forEach(row => {
                    const name = row.querySelector('.customer-name').textContent.toLowerCase();
                    const address = row.querySelector('.customer-address').textContent.toLowerCase();

                    // Check if name or address contains the search term
                    if (name.includes(searchTerm) || address.includes(searchTerm)) {
                        row.style.display = '';
                        resultsFound = true;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show/hide no results message
                if (resultsFound) {
                    noResultsMessage.style.display = 'none';
                    customerTable.style.display = '';
                } else {
                    noResultsMessage.style.display = 'block';
                    customerTable.style.display = 'none';
                }
            }

            // Search on button click
            searchButton.addEventListener('click', performSearch);

            // Search on Enter key press
            searchInput.addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    performSearch();
                }

                // If input is cleared, show all rows
                if (this.value === '') {
                    performSearch();
                }
            });

            // Real-time search (optional, can be commented out if not desired)
            searchInput.addEventListener('input', function() {
                // Add a small delay to avoid too many searches while typing
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(performSearch, 300);
            });

            // Payment Modal Functionality

            // Open payment modal when payment button is clicked
            paymentButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get customer data from the row
                    const row = this.closest('tr');
                    const customerName = row.querySelector('.customer-name').textContent;
                    const customerPackage = row.querySelector('td:nth-child(6)').textContent;
                    const billStatus = row.querySelector('td:nth-child(8) .badge').textContent
                        .trim();

                    // Set customer data in the modal
                    document.getElementById('customerName').textContent = customerName;
                    document.getElementById('customerPackage').textContent = customerPackage;
                    document.getElementById('billStatus').textContent = billStatus;

                    // Reset form state
                    document.getElementById('paymentForm').reset();
                    paymentDateInput.value = formattedDate;
                    paymentMethodSelect.value = '1';
                    transferDetails.style.display = 'none';

                    // Set payment amount (this would typically come from your backend)
                    // For demo purposes, we're setting a random amount
                    const amount = row.getAttribute('data-tagihan');
                    document.getElementById('paymentAmount').textContent = amount
                        .toLocaleString(
                            'id-ID');

                    // Show the modal
                    paymentModal.show();
                });
            });

            // Handle payment method selection with select dropdown
            paymentMethodSelect.addEventListener('change', function() {
                const method = this.value;

                // Show/hide transfer details based on selected method
                if (method === '2' || method === '3') {
                    transferDetails.style.display = 'block';
                } else {
                    transferDetails.style.display = 'none';
                }
            });


        });
    </script>

    <script>
        // Function to update customer counters (total, active, non-active)
        function updateCustomerCounters() {
            const tableBody = document.querySelector('#customerTable tbody');
            if (!tableBody) return;

            // Get all customer rows
            const allRows = tableBody.querySelectorAll('tr.customer-row');

            // Count total customers
            const totalCustomers = allRows.length;

            // Count active customers (status_id = 3)
            let activeCustomers = 0;
            allRows.forEach(row => {
                const statusCell = row.querySelector('td:nth-child(7)');
                if (statusCell && statusCell.textContent.trim().includes('Aktif')) {
                    activeCustomers++;
                }
            });

            // Count non-active customers (all others)
            const nonActiveCustomers = totalCustomers - activeCustomers;

            // Update the counter badges
            const totalBadge = document.querySelector('.data-card.primary-card .badge.bg-primary');
            const activeBadge = document.querySelector('.data-card.success-card .badge.bg-success');
            const nonActiveBadge = document.querySelector('.data-card.danger-card .badge.bg-danger');

            if (totalBadge) totalBadge.textContent = totalCustomers;
            if (activeBadge) activeBadge.textContent = activeCustomers;
            if (nonActiveBadge) nonActiveBadge.textContent = nonActiveCustomers;

            console.log(
                `Updated counters - Total: ${totalCustomers}, Active: ${activeCustomers}, Non-Active: ${nonActiveCustomers}`
            );
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize customer counters when page loads
            updateCustomerCounters();

            // Listen for real-time updates using Laravel Echo
            window.Echo.channel('updates-data')
                .listen('.data.updated', function(e) {
                    console.log('Received real-time update:', e);

                    // Show notification
                    Swal.fire({
                        text: e.notification.message,
                        icon: e.notification.type,
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        animation: true,
                        topLayer: true
                    });

                    if (e.data) {
                        // Ensure data is always an array
                        const dataArray = Array.isArray(e.data) ? e.data : [e.data];
                        updateTableData(dataArray); // Update table with customer data

                        // Update customer counters after data update
                        updateCustomerCounters();
                    }
                });

            function updateTableData(data) {
                const tableBody = document.querySelector('#customerTable tbody');
                if (!tableBody) {
                    console.log('Table body not found');
                    return;
                }

                if (!data || data.length === 0) {
                    console.log('No data received for update');
                    return;
                }

                // Log the data for debugging
                console.log('Updating table with data:', data);

                data.forEach((item) => {
                    const row = tableBody.querySelector(`tr[data-id="${item.id}"]`);
                    if (row) {
                        // Update existing row
                        const nameCell = row.querySelector('td:nth-child(2)');
                        const addressCell = row.querySelector('td:nth-child(3)');
                        const phoneCell = row.querySelector('td:nth-child(4)');
                        const serverCell = row.querySelector('td:nth-child(5)');
                        const paketCell = row.querySelector('td:nth-child(6)');
                        const statusCell = row.querySelector('td:nth-child(7)');
                        const tagihanCell = row.querySelector('td:nth-child(8)');

                        // Update basic customer info
                        if (nameCell && item.nama_customer) {
                            nameCell.textContent = item.nama_customer;
                        }

                        if (addressCell && item.alamat) {
                            addressCell.textContent = item.alamat;
                        }

                        if (phoneCell && item.no_hp) {
                            phoneCell.textContent = item.no_hp;
                        }

                        if (serverCell && item.getServer && item.getServer.lokasi_server) {
                            serverCell.textContent = item.getServer.lokasi_server;
                        }

                        if (paketCell && item.paket && item.paket.nama_paket) {
                            paketCell.innerHTML = `
                                <span class="badge bg-warning bg-opacity-10 text-warning status-badge">
                                    ${item.paket.nama_paket}
                                </span>
                            `;
                        }

                        if (statusCell) {
                            // Update status cell based on status_id
                            if (item.status_id == 3) {
                                statusCell.innerHTML =
                                    '<span class="badge bg-success bg-opacity-10 text-success status-badge">Aktif</span>';
                            } else if (item.status_id == 9) {
                                statusCell.innerHTML =
                                    '<span class="badge bg-danger status-badge">Blokir</span>';
                            } else if (item.status_id == 2) {
                                // Non-aktif status
                                statusCell.innerHTML =
                                    '<span class="badge bg-secondary bg-opacity-10 text-secondary status-badge">Non-Aktif</span>';
                            } else {
                                // Handle other status types
                                let statusName = '';
                                try {
                                    // Try to get status name from item.status if available
                                    statusName = item.status && item.status.nama_status ? item.status
                                        .nama_status : 'Status Lain';
                                } catch (e) {
                                    statusName = 'Status Lain';
                                }

                                statusCell.innerHTML =
                                    `<span class="badge bg-warning bg-opacity-10 text-warning status-badge">${statusName}</span>`;
                            }
                        }

                        // Update tagihan status if available
                        if (tagihanCell && item.invoice && item.invoice.length > 0 && item.invoice[0]
                            .status) {
                            try {
                                const statusName = item.invoice[0].status.nama_status || '';
                                const isLunas = statusName.toLowerCase() === 'sudah bayar';

                                tagihanCell.innerHTML = `
                                    <span class="badge bg-${isLunas ? 'success' : 'danger'} bg-opacity-10 text-${isLunas ? 'success' : 'danger'} status-badge">
                                        ${statusName}
                                    </span>
                                `;
                            } catch (e) {
                                console.log('Error updating tagihan status:', e);
                            }
                        }

                        // Update data-tagihan attribute for customer counters
                        if (item.invoice && item.invoice.length > 0 && item.invoice[0].status) {
                            const isLunas = item.invoice[0].status.nama_status.toLowerCase() ===
                                'sudah bayar';
                            const tagihan = isLunas ? '0' : (item.invoice[0].tagihan || '0');
                            row.setAttribute('data-tagihan', tagihan);
                        }
                    } else {
                        console.log(`Row with data-id="${item.id}" not found. Adding new row.`);

                        // Create a new row for this customer
                        try {
                            const newRow = document.createElement('tr');
                            newRow.className = 'customer-row';
                            newRow.setAttribute('data-id', item.id);

                            // Set data-tagihan attribute
                            if (item.invoice && item.invoice.length > 0 && item.invoice[0].status) {
                                const isLunas = item.invoice[0].status.nama_status.toLowerCase() ===
                                    'sudah bayar';
                                const tagihan = isLunas ? '0' : (item.invoice[0].tagihan || '0');
                                newRow.setAttribute('data-tagihan', tagihan);
                            } else {
                                newRow.setAttribute('data-tagihan', '0');
                            }

                            // Get the current row count for numbering
                            const rowCount = tableBody.querySelectorAll('tr').length + 1;

                            // Determine status badge HTML
                            let statusBadge = '';
                            if (item.status_id == 3) {
                                statusBadge =
                                    '<span class="badge bg-success bg-opacity-10 text-success status-badge">Aktif</span>';
                            } else if (item.status_id == 9) {
                                statusBadge = '<span class="badge bg-danger status-badge">Blokir</span>';
                            } else if (item.status_id == 2) {
                                statusBadge =
                                    '<span class="badge bg-secondary bg-opacity-10 text-secondary status-badge">Non-Aktif</span>';
                            } else {
                                statusBadge =
                                    '<span class="badge bg-warning bg-opacity-10 text-warning status-badge">Status Lain</span>';
                            }

                            // Determine tagihan status badge HTML
                            let tagihanBadge = '';
                            if (item.invoice && item.invoice.length > 0 && item.invoice[0].status) {
                                const isLunas = item.invoice[0].status.nama_status.toLowerCase() ===
                                    'sudah bayar';
                                tagihanBadge = `
                                    <span class="badge bg-${isLunas ? 'success' : 'danger'} bg-opacity-10 text-${isLunas ? 'success' : 'danger'} status-badge">
                                        ${item.invoice[0].status.nama_status}
                                    </span>
                                `;
                            }
                            // Create row HTML with all the necessary cells
                            newRow.innerHTML = `
                                <td class="text-center">${rowCount}</td>
                                <td class="customer-name">${item.nama_customer || ''}</td>
                                <td class="customer-address">${item.alamat || ''}</td>
                                <td>${item.no_hp || ''}</td>
                                <td>${item.getServer ? item.getServer.lokasi_server : ''}</td>
                                <td>
                                    <span class="badge bg-warning bg-opacity-10 text-warning status-badge">
                                        ${item.paket ? item.paket.nama_paket : ''}
                                    </span>
                                </td>
                                <td class="text-center">${statusBadge}</td>
                                <td class="text-center">${tagihanBadge || '<span class="badge bg-secondary status-badge">Belum Ada Invoice</span>'}</td>
                            `;

                            // Add the new row to the table
                            tableBody.appendChild(newRow);

                            // Initialize tooltips for new elements
                            const tooltips = newRow.querySelectorAll('[data-bs-toggle="tooltip"]');
                            tooltips.forEach(tooltip => {
                                new bootstrap.Tooltip(tooltip);
                            });
                        } catch (e) {
                            console.error('Error creating new row:', e);
                        }
                    }
                });
            }
        });
    </script>

@endsection
