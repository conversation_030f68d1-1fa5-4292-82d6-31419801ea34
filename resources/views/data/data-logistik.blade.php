@extends('layouts/contentNavbarLayout')

@section('title', ' Horizontal Layouts - Forms')

@section('content')
    <!-- Basic Layout & Basic with Icons -->
    <div class="row">
        <!-- Basic Layout -->
        <div class="col-xxl">
            <div class="card mb-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h4 class="mb-0">Data Logistik</h4>
                </div>

                <div class="card-body">
                    <div class="col-sm-12 mb-5">
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="offcanvas"
                            data-bs-target="#offcanvasBoth">
                            <i class="bx bxs-add-to-queue icon-sm me-2"></i> Tambah Stok
                        </button>
                    </div>
                    <div class="table-responsive text-nowrap">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><PERSON><PERSON></th>
                                    <th>Stok tersedia</th>
                                    <th><PERSON><PERSON></th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($perangkat as $p)
                                    <tr>
                                        <td>{{ $p->nama_perangkat }}</td>
                                        <td>{{ $p->jumlah_stok }}</td>
                                        <td>Rp {{ number_format($p->harga, 0, ',', '.') }}</td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-icon btn-warning btn-sm">
                                                <i class="bx bxs-edit-alt icon-sm"></i>
                                            </button>
                                            <button type="button" class="btn btn-icon btn-danger btn-sm">
                                                <i class="bx bxs-trash icon-sm"></i>
                                            </button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div class="mb-3">
                            <div class="stats-card p-3">
                                <div class="stat-item mb-3">
                                    <span class="text-muted fw-semibold">Total Stok:</span>
                                    <h5 class="mb-0 mt-1">{{ $perangkat->sum('jumlah_stok') }} Unit</h5>
                                </div>
                                <div class="stat-item">
                                    <span class="text-muted fw-semibold">Total Harga:</span>
                                    <h5 class="mb-0 mt-1 text-primary">Rp
                                        {{ number_format($perangkat->sum('harga'), 0, ',', '.') }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- modal --}}

    <div class="offcanvas offcanvas-start" data-bs-scroll="true" tabindex="-1" id="offcanvasBoth"
        aria-labelledby="offcanvasBothLabel">
        <div class="offcanvas-header bg-primary mb-2">
            <h5 id="offcanvasBothLabel" class="offcanvas-title text-white">Tambah Stok Logistik</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body my-auto">
            <form action="/logistik/store" method="POST">
                @csrf
                <div class="mb-6">
                    <label class="form-label" for="basic-default-fullname">Nama Perangkat</label>
                    <input type="text" class="form-control" id="basic-default-fullname" placeholder="F68273hb4u"
                        name="nama_perangkat">
                </div>
                <div class="mb-6">
                    <label class="form-label" for="basic-default-phone">Jumlah Perangkat</label>
                    <input name="jumlah_stok" type="text" id="basic-default-phone" class="form-control phone-mask"
                        placeholder="100">
                </div>
                <div class="mb-6">
                    <label class="form-label" for="harga-satuan">Harga Satuan</label>
                    <input name="harga" type="text" id="harga-satuan" class="form-control" placeholder="Rp."
                        oninput="formatRupiah(this)">
                </div>
                <div class="mb-6">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="offcanvas"
                        aria-label="Close">close</button>
                    <button type="submit" class="btn btn-primary btn-sm">Tambah</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function formatRupiah(input) {
            let value = input.value.replace(/[^\d]/g, '');
            if (value !== '') {
                value = parseInt(value);
                value = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
                input.value = 'Rp ' + value;
            }
        }
    </script>
@endsection
