@extends('layouts/contentNavbarLayout')

@section('title', ' Horizontal Layouts - Forms')

@section('content')

    <div class="row">
        <div class="col-xxl">
            <div class="card mb-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <h4 class="mb-0">User Management</h4>
                </div>
                <div class="card-body">
                    <div class="col-sm-12 mb-5">
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="offcanvas"
                            data-bs-target="#offcanvasBoth">
                            <i class="bx bxs-add-to-queue icon-sm me-2"></i> Tambah
                        </button>
                    </div>
                    <div class="col-sm-12 mb-5">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Nama</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($user->where('roles_id', '!=', 8) as $user)
                                        <tr>
                                            <td>{{ $user->name }}</td>
                                            <td>{{ $user->email }}</td>
                                            <td class="text-center"><span
                                                    class="badge bg-warning">{{ $user->roles->name }}</span>
                                            </td>
                                            <td class="text-center">
                                                <button type="button" class="btn btn-icon btn-warning btn-sm">
                                                    <i class="bx bxs-edit-alt icon-sm"></i>
                                                </button>
                                                <button type="button" class="btn btn-icon btn-danger btn-sm">
                                                    <i class="bx bxs-trash icon-sm"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal --}}
    <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvasBoth" aria-labelledby="offcanvasBothLabel">
        <div class="offcanvas-header bg-primary">
            <h5 id="offcanvasBothLabel" class="offcanvas-title text-white">Tambah User</h5>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body mt-3">
            <form action="/user/store" method="POST">
                @csrf
                <div class="row mb-3">
                    <label class="form-label">Nama</label>
                    <div class="col-sm-12">
                        <input type="text" class="form-control" name="name" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <label class="form-label">Email</label>
                    <div class="col-sm-12">
                        <input type="email" class="form-control" name="email" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <label class="form-label">Role</label>
                    <div class="col-sm-12">
                        <select class="form-select" name="roles_id" required>
                            <option selected disabled>Pilih Role</option>
                            @foreach ($role as $role)
                                <option value="{{ $role->id }}">{{ $role->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12 text-end mt-2">
                        <button type="submit" class="btn btn-primary btn-sm">Simpan</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

@endsection
