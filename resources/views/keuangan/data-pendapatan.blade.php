@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pendapatan')

@section('page-style')
    <style>
        .modern-container {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 1.5rem 0;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 2rem;
            color: white;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
        }

        .stat-card {
            border: none;
            border-radius: 12px;
            background: white;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .filter-card {
            background: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 1.5rem;
        }

        .search-input {
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        .btn-modern {
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-outline-modern {
            border: 1px solid #e3e6f0;
            background: white;
            color: #6c757d;
        }

        .btn-outline-modern:hover {
            background: #f8f9fa;
            border-color: #667eea;
            color: #667eea;
        }

        .data-table {
            background: white;
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .table-modern {
            margin-bottom: 0;
        }

        .table-modern thead th {
            background: #f8f9fa;
            border: none;
            padding: 1rem;
            font-weight: 600;
            color: #495057;
            font-size: 0.875rem;
        }

        .table-modern tbody td {
            padding: 1rem;
            border-top: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .table-modern tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-paid {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            border: none;
            font-size: 0.75rem;
            margin: 0 0.125rem;
            transition: all 0.3s ease;
        }

        .pagination-modern .page-link {
            border: none;
            border-radius: 6px;
            margin: 0 0.125rem;
            color: #6c757d;
        }

        .pagination-modern .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: transparent;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            border-radius: 12px;
        }

        .spinner {
            width: 2rem;
            height: 2rem;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .no-data i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .page-header {
                padding: 1.5rem;
                text-align: center;
            }

            .stat-card {
                margin-bottom: 1rem;
            }

            .filter-card .row>div {
                margin-bottom: 1rem;
            }

            .table-responsive {
                border-radius: 12px;
            }
        }
    </style>
@endsection

@section('content')
    <div class="modern-container">
        <div class="row">
            <!-- Page Header -->
            <div class="page-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2 fw-bold">Data Pendapatan</h2>
                        <p class="mb-0 opacity-75">Kelola dan pantau pendapatan dari invoice pelanggan</p>
                    </div>
                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                        <div class="d-flex align-items-center justify-content-md-end">
                            <i class="bx bx-money-withdraw me-2" style="font-size: 2rem;"></i>
                            <div>
                                <small class="d-block opacity-75">Total Invoice</small>
                                <strong style="font-size: 1.25rem;">{{ $totalInvoices ?? 0 }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-success bg-opacity-10 text-success">
                                    <i class="bx bx-money"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Total Pendapatan</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-success">
                                        <i class="bx bx-trending-up me-1"></i>Semua waktu
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-primary bg-opacity-10 text-primary">
                                    <i class="bx bx-calendar"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Bulan Ini</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($monthlyRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-primary">
                                        <i class="bx bx-calendar-check me-1"></i>{{ date('F Y') }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-warning bg-opacity-10 text-warning">
                                    <i class="bx bx-time-five"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Belum Bayar</p>
                                    <h4 class="mb-0 fw-bold text-dark">Rp
                                        {{ number_format($pendingRevenue ?? 0, 0, ',', '.') }}</h4>
                                    <small class="text-warning">
                                        <i class="bx bx-hourglass me-1"></i>Menunggu
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6 col-12 mb-3">
                    <div class="card stat-card h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info bg-opacity-10 text-info">
                                    <i class="bx bx-receipt"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <p class="text-muted mb-1 fw-medium">Total Invoice</p>
                                    <h4 class="mb-0 fw-bold text-dark">{{ number_format($totalInvoices ?? 0, 0, ',', '.') }}
                                    </h4>
                                    <small class="text-info">
                                        <i class="bx bx-file me-1"></i>Dokumen
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and Search -->
            <div class="card filter-card">
                <div class="card-body p-4">
                    <div class="row align-items-end">
                        <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Pencarian</label>
                            <div class="position-relative">
                                <input type="text" id="searchInput" class="form-control search-input"
                                    placeholder="Cari nama pelanggan atau paket..." value="{{ $search ?? '' }}">
                                <i class="bx bx-search position-absolute"
                                    style="right: 12px; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                            </div>
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Status</label>
                            <select id="statusFilter" class="form-select search-input">
                                <option value="">Semua Status</option>
                                @foreach ($statusOptions ?? [] as $status)
                                    <option value="{{ $status->id }}"
                                        {{ ($status->id ?? '') == $status->id ? 'selected' : '' }}>
                                        {{ $status->nama_status }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Dari Tanggal</label>
                            <input type="date" id="startDate" class="form-control search-input"
                                value="{{ $startDate ?? '' }}">
                        </div>

                        <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                            <label class="form-label fw-medium text-dark">Sampai Tanggal</label>
                            <input type="date" id="endDate" class="form-control search-input"
                                value="{{ $endDate ?? '' }}">
                        </div>

                        <div class="col-lg-2 col-md-12">
                            <div class="d-flex gap-2">
                                <button type="button" id="filterBtn" class="btn btn-modern btn-primary-modern flex-fill">
                                    <i class="bx bx-filter-alt me-1"></i>Filter
                                </button>
                                <button type="button" id="resetBtn" class="btn btn-modern btn-outline-modern">
                                    <i class="bx bx-refresh"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card data-table position-relative">
                <div id="loadingOverlay" class="loading-overlay d-none">
                    <div class="spinner"></div>
                </div>

                <div class="card-header border-0 bg-white p-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="stat-icon bg-info bg-opacity-10 text-info me-3">
                                    <i class="bx bx-table"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1 fw-bold text-dark">Daftar Pendapatan</h5>
                                    <p class="text-muted mb-0">Data invoice dan pembayaran pelanggan</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end mt-3 mt-md-0">
                            <small class="text-muted fw-medium">
                                <i class="bx bx-info-circle me-1"></i>
                                Menampilkan {{ $invoices->count() ?? 0 }} dari {{ $invoices->total() ?? 0 }} data
                            </small>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern" id="dataTable">
                            <thead>
                                <tr>
                                    <th class="sortable" data-sort="index">
                                        <i class="bx bx-hash me-1"></i>No
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="date">
                                        <i class="bx bx-calendar me-1"></i>Tanggal
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="customer">
                                        <i class="bx bx-user me-1"></i>Pelanggan
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="package">
                                        <i class="bx bx-package me-1"></i>Paket
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="amount">
                                        <i class="bx bx-money me-1"></i>Jumlah
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="due_date">
                                        <i class="bx bx-time me-1"></i>Jatuh Tempo
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-sort="status">
                                        <i class="bx bx-check-circle me-1"></i>Status
                                        <i class="bx bx-chevron-up sort-icon"></i>
                                    </th>
                                    <th>
                                        <i class="bx bx-cog me-1"></i>Aksi
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                @forelse($invoices ?? [] as $index => $invoice)
                                    <tr>
                                        <td class="fw-medium">{{ $invoices->firstItem() + $index }}</td>
                                        <td>{{ \Carbon\Carbon::parse($invoice->created_at)->format('d/m/Y') }}</td>
                                        <td>
                                            <div class="fw-medium text-dark">
                                                {{ $invoice->customer->nama_customer ?? 'N/A' }}</div>
                                            <small
                                                class="text-muted">{{ Str::limit($invoice->customer->alamat ?? '', 30) }}</small>
                                        </td>
                                        <td>
                                            <span
                                                class="badge bg-light text-dark border">{{ $invoice->paket->nama_paket ?? 'N/A' }}</span>
                                        </td>
                                        <td class="fw-bold text-dark">Rp
                                            {{ number_format($invoice->tagihan, 0, ',', '.') }}</td>
                                        <td>{{ \Carbon\Carbon::parse($invoice->jatuh_tempo)->format('d/m/Y') }}</td>
                                        <td>
                                            @php
                                                $statusClass = 'status-pending';
                                                if ($invoice->status->nama_status == 'Sudah Bayar') {
                                                    $statusClass = 'status-paid';
                                                } elseif (\Carbon\Carbon::parse($invoice->jatuh_tempo)->isPast()) {
                                                    $statusClass = 'status-overdue';
                                                }
                                            @endphp
                                            <span class="status-badge {{ $statusClass }}">
                                                {{ $invoice->status->nama_status ?? 'N/A' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <button class="btn btn-sm btn-outline-primary action-btn"
                                                    onclick="viewInvoice({{ $invoice->id }})" title="Lihat Detail">
                                                    <i class="bx bx-show"></i>
                                                </button>
                                                @if ($invoice->status->nama_status != 'Sudah Bayar')
                                                    <a href="{{ route('payment.show', $invoice->id) }}"
                                                        class="btn btn-sm btn-outline-success action-btn" title="Bayar">
                                                        <i class="bx bx-credit-card"></i>
                                                    </a>
                                                @endif
                                                <button class="btn btn-sm btn-outline-info action-btn"
                                                    onclick="printInvoice({{ $invoice->id }})" title="Cetak">
                                                    <i class="bx bx-printer"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="no-data">
                                            <i class="bx bx-inbox"></i>
                                            <h6 class="mt-2 mb-1">Tidak ada data</h6>
                                            <p class="mb-0">Belum ada data pendapatan yang tersedia</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                @if ($invoices->hasPages())
                    <div class="card-footer border-0 bg-white p-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                Menampilkan {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }}
                                dari {{ $invoices->total() ?? 0 }} data
                            </div>
                            <nav>
                                {{ $invoices->appends(request()->query())->links('pagination::bootstrap-4', ['class' => 'pagination-modern']) }}
                            </nav>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize variables
            let searchTimeout;
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            const filterBtn = document.getElementById('filterBtn');
            const resetBtn = document.getElementById('resetBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const tableBody = document.getElementById('tableBody');

            // Real-time search functionality
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch();
                }, 500); // Delay 500ms untuk menghindari terlalu banyak request
            });

            // Filter button click
            filterBtn.addEventListener('click', function() {
                performSearch();
            });

            // Reset button click
            resetBtn.addEventListener('click', function() {
                searchInput.value = '';
                statusFilter.value = '';
                startDate.value = '';
                endDate.value = '';
                performSearch();
            });

            // Status filter change
            statusFilter.addEventListener('change', function() {
                performSearch();
            });

            // Date filter change
            startDate.addEventListener('change', function() {
                performSearch();
            });

            endDate.addEventListener('change', function() {
                performSearch();
            });

            // Perform search function
            function performSearch() {
                showLoading();

                const searchParams = new URLSearchParams({
                    search: searchInput.value,
                    status: statusFilter.value,
                    start_date: startDate.value,
                    end_date: endDate.value
                });

                // Update URL without page reload
                const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
                window.history.pushState({}, '', newUrl);

                // Perform AJAX request
                fetch(`{{ route('pendapatan.ajax') }}?${searchParams.toString()}`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        updateTable(data.html);
                        updateStatistics(); // Update statistics if needed
                    })
                    .catch(error => {
                        hideLoading();
                        console.error('Error:', error);
                        showNotification('Terjadi kesalahan saat memuat data', 'error');
                    });
            }

            // Show loading overlay
            function showLoading() {
                loadingOverlay.classList.remove('d-none');
            }

            // Hide loading overlay
            function hideLoading() {
                loadingOverlay.classList.add('d-none');
            }

            // Update table content
            function updateTable(html) {
                const tableContainer = document.querySelector('.table-responsive');
                if (tableContainer && html) {
                    tableContainer.innerHTML = html;
                    initializeTableSorting(); // Re-initialize sorting after update
                }
            }

            // Update statistics (optional - if you want real-time stats update)
            function updateStatistics() {
                // This can be implemented to update the statistics cards
                // based on current filter results
            }

            // Table sorting functionality
            function initializeTableSorting() {
                const sortableHeaders = document.querySelectorAll('.sortable');

                sortableHeaders.forEach(header => {
                    header.addEventListener('click', function() {
                        const sortField = this.dataset.sort;
                        const currentSort = this.classList.contains('sort-asc') ? 'asc' : 'desc';
                        const newSort = currentSort === 'asc' ? 'desc' : 'asc';

                        // Remove sort classes from all headers
                        sortableHeaders.forEach(h => {
                            h.classList.remove('sort-asc', 'sort-desc');
                            const icon = h.querySelector('.sort-icon');
                            if (icon) {
                                icon.className = 'bx bx-chevron-up sort-icon';
                            }
                        });

                        // Add sort class to current header
                        this.classList.add(`sort-${newSort}`);
                        const icon = this.querySelector('.sort-icon');
                        if (icon) {
                            icon.className =
                                `bx bx-chevron-${newSort === 'asc' ? 'up' : 'down'} sort-icon`;
                        }

                        // Perform sort
                        sortTable(sortField, newSort);
                    });
                });
            }

            // Sort table function
            function sortTable(field, direction) {
                const tbody = document.getElementById('tableBody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                rows.sort((a, b) => {
                    let aVal, bVal;

                    switch (field) {
                        case 'index':
                            aVal = parseInt(a.cells[0].textContent);
                            bVal = parseInt(b.cells[0].textContent);
                            break;
                        case 'date':
                            aVal = new Date(a.cells[1].textContent.split('/').reverse().join('-'));
                            bVal = new Date(b.cells[1].textContent.split('/').reverse().join('-'));
                            break;
                        case 'customer':
                            aVal = a.cells[2].textContent.toLowerCase();
                            bVal = b.cells[2].textContent.toLowerCase();
                            break;
                        case 'package':
                            aVal = a.cells[3].textContent.toLowerCase();
                            bVal = b.cells[3].textContent.toLowerCase();
                            break;
                        case 'amount':
                            aVal = parseInt(a.cells[4].textContent.replace(/[^\d]/g, ''));
                            bVal = parseInt(b.cells[4].textContent.replace(/[^\d]/g, ''));
                            break;
                        case 'due_date':
                            aVal = new Date(a.cells[5].textContent.split('/').reverse().join('-'));
                            bVal = new Date(b.cells[5].textContent.split('/').reverse().join('-'));
                            break;
                        case 'status':
                            aVal = a.cells[6].textContent.toLowerCase();
                            bVal = b.cells[6].textContent.toLowerCase();
                            break;
                        default:
                            aVal = a.cells[0].textContent;
                            bVal = b.cells[0].textContent;
                    }

                    if (direction === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });

                // Clear tbody and append sorted rows
                tbody.innerHTML = '';
                rows.forEach(row => tbody.appendChild(row));
            }

            // Show notification function
            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className =
                    `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
                notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

                document.body.appendChild(notification);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 5000);
            }

            // Initialize table sorting on page load
            initializeTableSorting();

            // Auto-refresh data every 30 seconds (optional)
            setInterval(() => {
                if (!searchInput.value && !statusFilter.value && !startDate.value && !endDate.value) {
                    performSearch();
                }
            }, 30000);
        });

        // Global functions for button actions
        function viewInvoice(id) {
            // Implement view invoice functionality
            window.open(`/invoice/${id}`, '_blank');
        }

        function printInvoice(id) {
            // Implement print invoice functionality
            window.open(`/invoice/${id}/print`, '_blank');
        }

        // Add CSS for sorting icons
        const sortingCSS = `
    .sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: all 0.3s ease;
    }

    .sortable:hover {
        background-color: #f8f9fa !important;
    }

    .sort-icon {
        font-size: 0.75rem;
        margin-left: 0.25rem;
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .sortable.sort-asc .sort-icon,
    .sortable.sort-desc .sort-icon {
        opacity: 1;
        color: #667eea;
    }

    .sortable.sort-desc .sort-icon {
        transform: rotate(180deg);
    }
`;

        // Inject sorting CSS
        const style = document.createElement('style');
        style.textContent = sortingCSS;
        document.head.appendChild(style);
    </script>
@endsection
