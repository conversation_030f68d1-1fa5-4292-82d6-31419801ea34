/**
* Apex Charts
*/
@use '../../scss/_bootstrap-extended/include' as light;
@import '../../scss/_custom-variables/libs';
@import 'apexcharts-clevision/dist/apexcharts';

// Light style
@if $enable-light-style {
  .light-style {
    .apexcharts-canvas {
      .apexcharts-tooltip {
        background: light.$card-bg;
        border-color: light.$border-color;
        box-shadow: light.$box-shadow;
        .apexcharts-tooltip-title {
          background: light.$card-bg;
          border-color: light.$border-color;
          font-family: light.$font-family-base !important;
        }
        &.apexcharts-theme-light {
          color: light.$headings-color;
        }
        &.apexcharts-theme-dark {
          color: light.$white;
        }
        .apexcharts-tooltip-title {
          font-weight: light.$headings-font-weight;
        }
      }
      .apexcharts-xaxistooltip,
      .apexcharts-yaxistooltip {
        background: light.$body-bg;
        border-color: light.$border-color;
        color: light.$headings-color;
        &.apexcharts-xaxistooltip-bottom,
        &.apexcharts-yaxistooltip-bottom {
          &:after {
            border-bottom-color: light.$body-bg;
          }
          &:before {
            border-bottom-color: light.$border-color;
          }
        }
        &.apexcharts-xaxistooltip-left,
        &.apexcharts-yaxistooltip-left {
          &:after {
            border-left-color: light.$body-bg;
          }
          &:before {
            border-left-color: light.$border-color;
          }
        }
        &.apexcharts-xaxistooltip-right,
        &.apexcharts-yaxistooltip-right {
          &:after {
            border-right-color: light.$body-bg;
          }
          &:before {
            border-right-color: light.$border-color;
          }
        }
        &.apexcharts-xaxistooltip-top,
        &.apexcharts-yaxistooltip-top {
          &:after {
            border-top-color: light.$body-bg;
          }
          &:before {
            border-top-color: light.$border-color;
          }
        }
      }
      .apexcharts-tooltip-text {
        font-family: light.$font-family-base !important;
        filter: none;
      }
    }
  }
}
// bar charts with radius hover fix
#orderStatisticsChart,
#totalRevenueChart,
#expensesBarChart {
  .apexcharts-xcrosshairs.apexcharts-active,
  .apexcharts-ycrosshairs.apexcharts-active {
    opacity: 0;
  }
}
