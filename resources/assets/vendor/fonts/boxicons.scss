$boxicons-font-path: 'boxicons';
$boxicons-font-size-base: 16px;

@import 'boxicons/css/boxicons';

.bx {
  vertical-align: middle;
  font-size: 1.25rem;
  line-height: 1;
}
// Override font path
@font-face {
  font-family: 'boxicons';
  font-weight: normal;
  font-style: normal;

  src: url('../fonts/#{$boxicons-font-path}/boxicons.eot');
  src:
    url('../fonts/#{$boxicons-font-path}/boxicons.eot') format('embedded-opentype'),
    url('../fonts/#{$boxicons-font-path}/boxicons.woff2') format('woff2'),
    url('../fonts/#{$boxicons-font-path}/boxicons.woff') format('woff'),
    url('../fonts/#{$boxicons-font-path}/boxicons.ttf') format('truetype'),
    url('../fonts/#{$boxicons-font-path}/boxicons.svg?#boxicons') format('svg');
}

// icon sizes

.bx-xs {
  font-size: 1rem !important;
}
.bx-sm {
  font-size: 1.125rem !important;
}
.bx-md {
  font-size: 1.375rem !important;
}
.bx-lg {
  font-size: 1.5rem !important;
}
.bx-6px {
  &,
  &:before {
    font-size: 6px;
  }
}
.bx-8px {
  &,
  &:before {
    font-size: 8px;
  }
}
.bx-10px {
  &,
  &:before {
    font-size: 10px;
  }
}
.bx-12px {
  &,
  &:before {
    font-size: 12px;
  }
}
.bx-14px {
  &,
  &:before {
    font-size: 14px;
  }
}
.bx-16px {
  &,
  &:before {
    font-size: 16px;
  }
}
.bx-18px {
  &,
  &:before {
    font-size: 18px;
  }
}
.bx-20px {
  &,
  &:before {
    font-size: 20px;
  }
}
.bx-22px {
  &,
  &:before {
    font-size: 22px;
  }
}
.bx-24px {
  &,
  &:before {
    font-size: 24px;
  }
}
.bx-26px {
  &,
  &:before {
    font-size: 26px;
  }
}
.bx-28px {
  &,
  &:before {
    font-size: 28px;
  }
}
.bx-30px {
  &,
  &:before {
    font-size: 30px;
  }
}
.bx-32px {
  &,
  &:before {
    font-size: 32px;
  }
}
.bx-36px {
  &,
  &:before {
    font-size: 36px;
  }
}
.bx-40px {
  &,
  &:before {
    font-size: 40px;
  }
}
.bx-42px {
  &,
  &:before {
    font-size: 42px;
  }
}
.bx-48px {
  &,
  &:before {
    font-size: 48px;
  }
}
