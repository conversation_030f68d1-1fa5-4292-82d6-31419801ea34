// Checkboxes and Radios
// *******************************************************************************
.form-check-input {
  cursor: $form-check-label-cursor;
  &:disabled {
    background-color: $form-check-input-disabled-bg;
    border-color: $form-check-input-disabled-bg;
  }
  &:checked {
    box-shadow: $box-shadow-sm;
  }
}

.form-check {
  position: relative;
}

.form-check:not(.form-switch) {
  .form-check-input[type='radio'] {
    background-size: 1.3125rem;
    &:not(:checked) {
      background-size: 0.75rem;
    }
  }
}
// Switches
// *******************************************************************************

.form-switch .form-check-input {
  background-color: $form-switch-bg;
  border: none;
  box-shadow: $form-switch-box-shadow;
  &:focus {
    box-shadow: $form-switch-box-shadow;
  }
}
