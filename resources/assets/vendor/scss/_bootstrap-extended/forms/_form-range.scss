// Range select
// *******************************************************************************

.form-range {
  // Chrome specific
  &::-webkit-slider-thumb {
    box-shadow: $form-range-thumb-box-shadow;

    &:hover {
      box-shadow: 0 0 0 8px rgba($primary, 0.16);
    }
    &:active,
    &:focus {
      box-shadow: 0 0 0 13px rgba($primary, 0.16);
      border-color: $primary;
    }
  }
  &::-webkit-slider-runnable-track {
    background-color: $primary;
  }

  // Mozilla specific
  &::-moz-range-thumb {
    box-shadow: $form-range-thumb-box-shadow;
    &:hover {
      box-shadow: 0 0 0 8px rgba($primary, 0.16);
    }
    &:active,
    &:focus {
      box-shadow: 0 0 0 13px rgba($primary, 0.16);
      border-color: $primary;
    }
  }

  &::-moz-range-track {
    background-color: $primary;
  }
  &:disabled {
    &::-webkit-slider-runnable-track {
      background-color: $form-range-track-disabled-bg;
    }

    &::-moz-range-track {
      background-color: $form-range-track-disabled-bg;
    }
  }
}
