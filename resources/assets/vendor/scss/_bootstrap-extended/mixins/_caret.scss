// * Carets
// *******************************************************************************

@mixin caret-up($caret-width) {
  margin-top: 0.97 * divide($caret-width, 2);
  margin-left: 0.8em;
  width: $caret-width;
  height: $caret-width;
  border: 2px solid;
  border-bottom: 0;
  border-left: 0;
  transform: rotate(-45deg);
}

@mixin caret-down($caret-width) {
  margin-top: -1.07 * divide($caret-width, 2);
  margin-left: 0.8em;
  width: $caret-width;
  height: $caret-width;
  border: 2px solid;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg);
}

@mixin caret-left($caret-width) {
  margin-top: 0;
  margin-right: 0.5em;
  width: $caret-width;
  height: $caret-width;
  border: 2px solid;
  border-top: 0;
  border-right: 0;
  transform: rotate(45deg);
}

@mixin caret-right($caret-width) {
  margin-top: 0;
  margin-right: 0.5em;
  width: $caret-width;
  height: $caret-width;
  border: 2px solid;
  border-top: 0;
  border-left: 0;
  transform: rotate(-45deg);
}
