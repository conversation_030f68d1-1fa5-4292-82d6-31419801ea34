// ? Theme related styles common styles

@import '../_components/include';

// Space above detached navbar (vertical layout only)
.layout-navbar-fixed .layout-wrapper:not(.layout-horizontal) .layout-page:before {
  content: '';
  width: 100%;
  height: 0.75rem;
  position: fixed;
  top: 0px;
  z-index: 10;
}

.bg-menu-theme {
  // Menu header
  .menu-header {
    position: relative;
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      width: 1rem;
      height: 1px;
      transition: all $menu-animation-duration ease-in-out;
    }
  }

  .menu-inner .menu-item .menu-link {
    .layout-wrapper:not(.layout-horizontal) & {
      border-radius: $border-radius;
    }
  }
  .menu-inner > .menu-item > .menu-link {
    .layout-horizontal & {
      border-radius: $border-radius;
    }
  }

  .menu-inner > {
    // Spacing and Box-shadow only for horizontal menu above lg screen
    @include media-breakpoint-up(xl) {
      .menu-item {
        .layout-horizontal & {
          margin: $menu-horizontal-link-padding-y 0;
          &:not(:first-child) {
            margin-left: calc($menu-item-spacer + 0.0625rem);
          }
          &:not(:last-child) {
            margin-right: calc($menu-item-spacer + 0.0625rem);
          }
          .menu-sub {
            box-shadow: $menu-horizontal-menu-box-shadow;
            z-index: 1;
          }
        }
      }
    }
    .menu-item.active:before {
      .layout-wrapper:not(.layout-horizontal) & {
        content: '';
        position: absolute;
        right: 0;
        width: 0.25rem;
        height: 2.6845rem;
        border-radius: $border-radius 0 0 $border-radius;
      }
    }
  }

  // Sub menu item link bullet
  .menu-sub > .menu-item > .menu-link:before {
    content: '';
    position: absolute;
    left: 1.4375rem;
    width: 0.375rem;
    height: 0.375rem;
    border-radius: 50%;

    // For horizontal layout
    .layout-horizontal & {
      left: 1.3rem;
    }
  }
  // ? Hide bullet from first child only as we display icon instead (horizontal)
  .menu-horizontal-wrapper > .menu-inner > .menu-item > .menu-sub > .menu-item > .menu-link:before {
    display: none;
  }
  // Sub menu item link active bullet
  .menu-sub > .menu-item.active > .menu-link:not(.menu-toggle):before {
    left: 1.1875rem;
    width: 0.875rem;
    height: 0.875rem;

    // For horizontal layout
    .layout-horizontal & {
      left: 1.1rem;
    }
  }
}

.layout-menu-hover .layout-menu {
  box-shadow: $box-shadow-lg;
  transition: all $menu-animation-duration ease-in-out;
}

.app-brand .layout-menu-toggle {
  position: absolute;
  inset-inline-start: $menu-width - 1.05rem;
  border-radius: 50%;
  opacity: 1;
  z-index: 3;
  i {
    width: 1.375rem;
    height: 1.375rem;
    transition: all $menu-animation-duration ease-in-out;
    line-height: 1;
  }
  @include media-breakpoint-down(xl) {
    display: none !important;
    .layout-menu-expanded & {
      display: block !important;
    }
  }
}

.menu-vertical {
  .menu-item {
    position: relative;
  }
}
