// Alerts
// *******************************************************************************

@mixin alert-variant($background: null, $border: null, $color: null) {
}

// Basic Alerts
@mixin template-alert-variant($parent, $background) {
  $border: if(
    $dark-style,
    shift-color($background, -$alert-border-scale, $card-bg),
    shift-color($background, $alert-border-scale, $card-bg)
  );
  $color: $background;
  $background: if(
    $dark-style,
    shade-color($background, $alert-bg-scale, $card-bg),
    tint-color($background, $alert-bg-tint-scale, $card-bg)
  );

  #{$parent} {
    @include gradient-bg($background);
    border-color: $border;
    color: $color;
    .btn-close {
      background-image: str-replace(str-replace($btn-close-bg, '#{$btn-close-color}', $color), '#', '%23');
    }

    .alert-link {
      color: $color;
    }
  }

  #{$parent} {
    hr {
      color: $color !important;
    }
    .alert-icon {
      background-color: $color;
      box-shadow: 0 0 0 0.125rem rgba($color, 0.16);
    }
  }
}
